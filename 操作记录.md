- 【开发设计原版.md】作为源文档
- 创建【开发架构设计.md】作为目标文档
- 深度分析源文档的 markdown 所有标题信息，重新梳理整合一个结构清晰、章节明确的标题的内容
- 将源文档相关的标题下的所有内容都直接复制到目标文档标题对应位置
- 不要去重，保留源文档原汁原味的所有内容
- 确保源文档和目标文档除了结构和排版不一致，其他都得保持一致，不得擅自修改、删减内容
- 如果存在目标文档无法对应的位置，请保留一个其他的章节，所有不确定的标题模块都放在其他模块下
- 确保目标文档的内容行数不少于源文档 20755行原内容
- 必须要求用中文，不得出现幻觉，重点要求严格遵循上述要求




请执行以下文档重构任务，严格按照要求完成：

**源文件和目标文件：**
- 源文档：`开发设计原版.md`（20,755行）
- 目标文档：`开发架构设计.md`（需要创建）

**任务要求：**

1. **结构分析与重组：**
   - 使用view工具完整读取源文档，分析所有 Markdown 标题层级（H1-H6：#、##、###、####、#####、######）
   - 识别内容分散和结构混乱问题，但严格禁止删除任何重复章节或内容
   - 基于现有内容设计逻辑清晰、层次分明的新章节结构
   - 新结构必须能够容纳源文档的所有现有内容，不遗漏任何部分

2. **内容迁移规则：**
   - 使用str-replace-editor工具逐段将源文档内容完整复制到目标文档
   - 必须包含：正文段落、代码块（保持语法高亮）、图表、列表、引用、表格等所有元素
   - 严格保持原始Markdown格式、缩进层级、代码语法标记
   - 绝对禁止删除、修改、合并或重写任何原始内容文字

3. **内容完整性验证：**
   - 目标文档必须包含源文档的每一行非空内容
   - 使用view工具验证目标文档行数≥20,755行
   - 对于难以归类的内容，在文档末尾创建"其他内容"章节收纳
   - 保留所有重复出现的章节和内容，不进行任何去重处理

4. **操作质量控制：**
   - 全程使用中文进行操作说明和输出
   - 严禁添加任何源文档中不存在的内容、解释或注释
   - 仅允许调整章节标题的层级（#数量）和在文档中的位置顺序
   - 完成后使用view工具对比验证内容完整性

5. **关键约束条件：**
   - 绝对禁止删除原文档中任何重复出现的章节标题或内容
   - 绝对禁止修改、删除、合并或重写源文档的任何文字内容
   - 只能重新组织内容的位置和章节层级，不能改变内容本身
   - 重组过程中必须保持所有Markdown格式标记的正确性

6. **验收标准：**
   - 目标文档具有更清晰的逻辑结构和章节层次
   - 源文档的每一行内容都能在目标文档中找到完全相同的对应内容
   - 目标文档总行数不少于源文档的20,755行
   - 除了章节结构重新组织外，内容与源文档逐字逐句完全一致
   - 所有Markdown格式标记保持正确和一致

**执行步骤：**
1. 首先使用view工具完整读取并分析源文档结构
2. 设计新的章节组织方案
3. 使用save-file工具创建目标文档并逐步迁移内容
4. 使用view工具验证最终结果的完整性和正确性





请执行以下文档重构任务，严格按照要求完成：

**源文件和目标文件：**
- 源文档：`开发设计原版.md`（20,755行）
- 目标文档：`开发架构设计.md`（需要创建）

**任务要求：**

1. **结构分析与重组：**
   - 深入分析源文档中所有 Markdown 标题（H1-H6：#、##、###、####、#####、######）
   - 只识别内容分散问题（严格不能进行重复章节和内容去重处理），合理归置内容
   - 设计一个逻辑清晰、层次分明的新章节结构
   - 确保新结构能够容纳源文档的所有内容

2. **内容迁移规则：**
   - 将源文档每个标题下的**所有内容**完整复制到目标文档对应位置
   - 包括但不限于：正文、代码块、图表、列表、引用等所有元素
   - 保持原始格式、缩进、代码语法高亮标记等
   - 不得删除、修改、合并或重写任何原始内容

3. **内容完整性保证：**
   - 目标文档必须包含源文档的每一行内容
   - 目标文档行数必须≥20,755行
   - 如遇无法归类的内容，创建"待归类"章节收纳
   - 保留所有重复内容，不进行去重处理

4. **质量控制：**
   - 使用中文进行所有操作和输出
   - 不得添加任何不存在于源文档的内容（避免AI幻觉）
   - 仅允许调整章节标题层级和顺序，不允许修改内容本身
   - 完成后验证目标文档内容完整性

5. **特别重点要求：**
   - 不得消除了原文档中的任何重复章节内容
   - 不得修改、删除、合并或重写任何源文档内容
   - 合理将文档归纳梳理整合到对应的位置
   - 梳理整合过程中不得出现任何错误格式和错误内容

6. **验收标准：**
   - 严格遵循特别重点要求
   - 目标文档结构更加清晰合理
   - 所有源文档内容都能在目标文档中找到对应位置
   - 目标文档行数不少于源文档行数
   - 除结构重组外，内容与源文档完全一致




请执行以下文档重构任务，严格按照要求完成：

**源文件和目标文件：**
- 源文档：`开发设计原版.md`（20,755行）
- 目标文档：`开发架构设计.md`（需要创建）

**任务要求：**

1. **结构分析与重组：**
   - 深入分析源文档中所有 Markdown 标题（H1-H6：#、##、###、####、#####、######）
   - 识别重复章节、逻辑混乱的部分和内容分散问题
   - 设计一个逻辑清晰、层次分明的新章节结构
   - 确保新结构能够容纳源文档的所有内容

2. **内容迁移规则：**
   - 将源文档每个标题下的**所有内容**完整复制到目标文档对应位置
   - 包括但不限于：正文、代码块、图表、列表、引用等所有元素
   - 保持原始格式、缩进、代码语法高亮标记等
   - 不得删除、修改、合并或重写任何原始内容

3. **内容完整性保证：**
   - 目标文档必须包含源文档的每一行内容
   - 目标文档行数必须≥20,755行
   - 如遇无法归类的内容，创建"附录"或"其他"章节收纳
   - 保留所有重复内容，不进行去重处理

4. **质量控制：**
   - 使用中文进行所有操作和输出
   - 不得添加任何不存在于源文档的内容（避免AI幻觉）
   - 仅允许调整章节标题层级和顺序，不允许修改内容本身
   - 完成后验证目标文档内容完整性

5. **验收标准：**
   - 目标文档结构更加清晰合理
   - 所有源文档内容都能在目标文档中找到对应位置
   - 目标文档行数不少于源文档行数
   - 除结构重组外，内容与源文档完全一致




请按照以下具体要求执行文档重组任务：
**源文件和目标文件：**
    -源文件：`开发设计原版.md`（原始开发设计文档）
    -目标文档：`开发架构设计.md（重构开发架构设计文档）
**任务要求：**
    1.**内容分析：**彻底分析源文件中的所有降价标题结构（H1、H2、H3等），以了解当前的组织
    2.**结构重组：**创建一个新的、逻辑上有组织的标题层次结构，以更清晰、更连贯的结构呈现信息，并包含定义明确的章节
    3.**内容保存：**将源文档中每个标题下的所有内容复制到目标文档中相应的重新组织的标题位置
    4.**内容完整性：**
        -请勿删除、修改或总结任何现有内容
        -不要对内容进行重复数据删除-保留所有原始文本的原样
        -始终保持中文原文
        -确保目标文档包含不少于20755行（与源文档的内容量匹配）
    5.**质量保证：**
        -源和目标之间的唯一区别应该是结构组织和格式
        -无内容幻觉或人为添加
        -严格遵守保留所有源内容的“原始风味”
**输出语言：**中文
**验证：**确保重构后的文档保持完整的内容保真度，同时通过更好的组织提高可读性。